"use client";

import React, { useMemo } from "react";
import Plot from "react-plotly.js";
import type { HFOEvent } from "@/types/hfo";
import { HFO_TYPE_COLORS } from "@/types/hfo";

const MAX_POINTS_PER_TRACE = 5000;

const decimateSeries = (time: number[], values: number[], maxPoints: number) => {
  if (time.length <= maxPoints) {
    return { time, values };
  }

  const step = Math.ceil(time.length / maxPoints);
  const decimatedTime: number[] = [];
  const decimatedValues: number[] = [];

  for (let i = 0; i < time.length; i += step) {
    const windowEnd = Math.min(i + step, time.length);
    let extremumIndex = i;
    let extremumValue = values[i];

    for (let j = i + 1; j < windowEnd; j++) {
      if (Math.abs(values[j]) > Math.abs(extremumValue)) {
        extremumIndex = j;
        extremumValue = values[j];
      }
    }

    decimatedTime.push(time[extremumIndex]);
    decimatedValues.push(extremumValue);
  }

  return { time: decimatedTime, values: decimatedValues };
};

interface ChannelRowProps {
  channelName: string;
  data: number[];
  timeWindow: [number, number];
  samplingRate: number;
  height?: number;
  showHFOMarkers?: boolean;
  hfoEvents?: HFOEvent[];
  gain?: number;
  samplingInfo?: {
    downsampled: boolean;
    effective_sampling_rate: number;
    original_sampling_rate: number;
    duration_seconds: number;
    downsample_factor?: number;
  };
}

export const ChannelRow: React.FC<ChannelRowProps> = ({
  channelName,
  data,
  timeWindow,
  samplingRate,
  height = 80,
  showHFOMarkers = false,
  hfoEvents = [],

  samplingInfo,
}) => {
  const plotData = useMemo(() => {
    if (!data || data.length === 0) {
      return [];
    }

    // Use effective sampling rate if data was downsampled
    const effectiveSamplingRate = samplingInfo?.downsampled ? samplingInfo.effective_sampling_rate : samplingRate;

    // Create time axis based on the actual sampling rate of the data
    const timeAxis = data.map((_, i) => i / effectiveSamplingRate);

    // Calculate indices based on the effective sampling rate
    const startIdx = Math.floor(timeWindow[0] * effectiveSamplingRate);
    const endIdx = Math.floor(timeWindow[1] * effectiveSamplingRate);
    const windowedTime = timeAxis.slice(startIdx, endIdx);
    const windowedData = data.slice(startIdx, endIdx);

    const { time: plotTime, values: plotValues } = decimateSeries(windowedTime, windowedData, MAX_POINTS_PER_TRACE);

    const traces: Partial<Plotly.Data>[] = [
      {
        x: plotTime,
        y: plotValues,
        type: "scatter",
        mode: "lines",
        name: channelName,
        line: { color: "#000000", width: 0.8 },
        hovertemplate: `Time: %{x:.2f}s<br>Amplitude: %{y:.2f}μV<extra></extra>`,
      },
    ];

    if (showHFOMarkers && hfoEvents.length > 0 && windowedTime.length > 0) {
      const channelHFOs = hfoEvents.filter((hfo) => hfo.channel === channelName);

      channelHFOs.forEach((hfo) => {
        const eventType = hfo.type || "accepted";
        const color = HFO_TYPE_COLORS[eventType] || HFO_TYPE_COLORS.accepted;

        const rawEndTime = Number.isFinite(hfo.end_time) ? hfo.end_time : undefined;
        const eventEndTime = rawEndTime && rawEndTime > hfo.start_time ? rawEndTime : hfo.start_time + 1 / effectiveSamplingRate;

        const overlapStart = Math.max(hfo.start_time, timeWindow[0]);
        const overlapEnd = Math.min(eventEndTime, timeWindow[1]);
        if (overlapEnd <= overlapStart) {
          return;
        }

        const eventStartIdx = Math.max(startIdx, Math.floor(overlapStart * effectiveSamplingRate));
        const eventEndIdx = Math.min(endIdx, Math.ceil(overlapEnd * effectiveSamplingRate));
        const localStart = Math.max(0, eventStartIdx - startIdx);
        const localEnd = Math.max(localStart + 1, eventEndIdx - startIdx);

        const segmentTime = windowedTime.slice(localStart, localEnd);
        const segmentValues = windowedData.slice(localStart, localEnd);
        if (segmentTime.length === 0) {
          return;
        }

        traces.push({
          x: segmentTime,
          y: segmentValues,
          type: "scatter",
          mode: "lines",
          line: { color, width: 1.6 },
          hovertemplate: `HFO (${eventType})<br>Time: %{x:.2f}s<extra></extra>`,
          showlegend: false,
          name: `${channelName}-${eventType}`,
        });
      });
    }

    return traces;
  }, [data, channelName, timeWindow, samplingRate, samplingInfo, showHFOMarkers, hfoEvents]);

  const layout = useMemo(() => {
    return {
      xaxis: {
        range: timeWindow,
        showgrid: true,
        gridcolor: "#f0f0f0",
        gridwidth: 0.5,
        showticklabels: false,
        zeroline: false,
        showline: false,
      },
      yaxis: {
        showgrid: false,
        showticklabels: false,
        zeroline: false,
        showline: false,
        autorange: true,
      },
      height: height,
      margin: { l: 120, r: 10, t: 5, b: 5 },
      hovermode: "x" as const,
      showlegend: false,
      plot_bgcolor: "#ffffff",
      paper_bgcolor: "#ffffff",
      shapes: [],
      annotations: [
        {
          x: 0,
          y: 0.5,
          xref: "paper" as const,
          yref: "paper" as const,
          text: `<b>${channelName}</b>`,
          showarrow: false,
          xanchor: "right" as const,
          xshift: -10,
          font: {
            size: 11,
            color: "#4a5568",
            family: "var(--font-sans), system-ui, -apple-system, sans-serif",
          },
        },
      ],
    };
  }, [channelName, timeWindow, height]);

  const config = {
    displayModeBar: false,
    responsive: true,
    staticPlot: false,
  };

  return (
    <div className="border-b border-gray-200 hover:bg-gray-50/50 transition-colors">
      <Plot data={plotData} layout={layout} config={config} style={{ width: "100%", height: `${height}px` }} />
    </div>
  );
};

export default React.memo(ChannelRow);
