import React, { useRef, useEffect, useState } from "react";
import type { HFOEvent } from "@/types/hfo";
import ChannelRow from "./ChannelRow";

interface ChannelGridProps {
  channelData: Record<string, number[]>;
  visibleChannels: string[];
  timeWindow: [number, number];
  samplingRate: number;
  hfoEvents?: HFOEvent[];
  showHFOMarkers?: boolean;
  gain?: number;
  showThresholds?: boolean;
  samplingInfo?: {
    downsampled: boolean;
    effective_sampling_rate: number;
    original_sampling_rate: number;
    duration_seconds: number;
    downsample_factor?: number;
  };
}

export const ChannelGrid: React.FC<ChannelGridProps> = ({
  channelData,
  visibleChannels,
  timeWindow,
  samplingRate,
  hfoEvents = [],
  showHFOMarkers = true,
  gain = 20,
  samplingInfo,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerHeight, setContainerHeight] = useState<number>(0);

  // Calculate container height on mount and resize
  useEffect(() => {
    const updateHeight = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        setContainerHeight(rect.height);
      }
    };

    updateHeight();
    window.addEventListener("resize", updateHeight);

    // Use ResizeObserver for more accurate container size tracking
    const resizeObserver = new ResizeObserver(updateHeight);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => {
      window.removeEventListener("resize", updateHeight);
      resizeObserver.disconnect();
    };
  }, []);

  if (visibleChannels.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center text-gray-500">
        <div className="text-center">
          <p className="text-sm">No channels selected</p>
          <p className="text-xs mt-1">Select channels from the panel to view their signals</p>
        </div>
      </div>
    );
  }

  // Calculate dynamic height per channel to fit all channels without scrolling
  const minChannelHeight = 40; // Minimum height for readability
  const maxChannelHeight = 120; // Maximum height to prevent overly large channels
  const availableHeight = containerHeight > 0 ? containerHeight : 600; // Fallback height

  // Calculate height per channel, ensuring all channels fit in the container
  const calculatedHeight = Math.max(minChannelHeight, Math.min(maxChannelHeight, Math.floor(availableHeight / visibleChannels.length)));

  // Use individual ChannelRow components for each channel
  return (
    <div ref={containerRef} className="w-full h-full overflow-y-auto overflow-x-hidden bg-white">
      <div className="min-w-full h-full">
        {visibleChannels.map((channel) => {
          const data = channelData?.[channel];
          if (!data || data.length === 0) {
            return null;
          }

          return (
            <ChannelRow
              key={channel}
              channelName={channel}
              data={data}
              timeWindow={timeWindow}
              samplingRate={samplingRate}
              height={calculatedHeight}
              showHFOMarkers={showHFOMarkers}
              hfoEvents={hfoEvents}
              gain={gain}
              samplingInfo={samplingInfo}
            />
          );
        })}
      </div>
    </div>
  );
};
