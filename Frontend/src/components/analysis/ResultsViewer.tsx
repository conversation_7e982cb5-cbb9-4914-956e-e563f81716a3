import React, { useEffect, useState, useCallback, useRef, useMemo } from "react";
import { Download } from "lucide-react";
import { apiClient } from "@/services/api";
import type { HFOEvent, HFOType } from "@/types/hfo";
import { ChannelGrid } from "./ChannelGrid";
import { ChannelSelector } from "./ChannelSelector";
import { TimeNavigationControls } from "./TimeNavigationControls";

const CHUNK_SECONDS = 10;
const DEFAULT_MIN_WINDOW_SECONDS = 0.5;
const ZOOM_IN_FACTOR = 0.8;
const ZOOM_OUT_FACTOR = 1.25;
const PAN_STEP_RATIO = 0.2;

interface AnalysisResults {
  metadata: {
    filename: string;
    sampling_rate: number;
    duration_seconds: number;
    channels: string[];
    processing_time: number;
    montage?: string;
    low_cutoff?: number;
    high_cutoff?: number;
  };
  statistics: {
    total_hfos: number;
    hfo_density: number;
    channels_with_hfos: string[];
    hfo_rate_per_channel?: Record<string, number>;
  };
  channel_data?: Record<string, number[]>;
  sampling_info?: {
    downsampled: boolean;
    effective_sampling_rate: number;
    original_sampling_rate: number;
    duration_seconds: number;
    downsample_factor?: number;
  };
  hfo_events: HFOEvent[];
  download_urls?: {
    results_json: string;
    hfo_events_csv: string;
  };
}

interface ResultsViewerProps {
  jobId: string;
  onClose?: () => void;
}

export const ResultsViewer: React.FC<ResultsViewerProps> = ({ jobId, onClose }) => {
  const [results, setResults] = useState<AnalysisResults | null>(null);
  const [channelData, setChannelData] = useState<Record<string, number[]> | null>(null);
  const [loading, setLoading] = useState(true);
  const [channelDataLoading, setChannelDataLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedChannels, setSelectedChannels] = useState<string[]>([]);
  const [timeWindow, setTimeWindow] = useState<[number, number]>([0, 0]);
  const [timeWindowSize, setTimeWindowSize] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [visibleHFOTypes] = useState<HFOType[]>(["accepted", "rejected", "rejected_long", "lfo_rejected", "noise_rejected"]);
  const [currentChunk, setCurrentChunk] = useState(0);

  const duration = results?.metadata.duration_seconds ?? 0;
  const minWindowSize = useMemo(() => {
    if (!duration) {
      return DEFAULT_MIN_WINDOW_SECONDS;
    }
    return Math.min(DEFAULT_MIN_WINDOW_SECONDS, duration);
  }, [duration]);

  const setWindow = useCallback(
    (start: number, width: number, { updateSize = true }: { updateSize?: boolean } = {}) => {
      if (!duration) {
        return;
      }

      const clampedWidth = Math.max(minWindowSize, Math.min(width, duration));
      const maxStart = Math.max(0, duration - clampedWidth);
      const clampedStart = Math.min(Math.max(0, start), maxStart);
      const clampedEnd = clampedStart + clampedWidth;

      setTimeWindow([clampedStart, clampedEnd]);
      if (updateSize) {
        setTimeWindowSize(clampedWidth);
      }
      setCurrentChunk(Math.floor(clampedStart / CHUNK_SECONDS));
    },
    [duration, minWindowSize]
  );

  const panWindow = useCallback(
    (direction: -1 | 1) => {
      if (!duration || timeWindowSize === 0) {
        return;
      }

      const step = Math.max(timeWindowSize * PAN_STEP_RATIO, duration * 0.01);
      setWindow(timeWindow[0] + direction * step, timeWindowSize, { updateSize: false });
    },
    [duration, timeWindow, timeWindowSize, setWindow]
  );

  const zoomWindow = useCallback(
    (direction: "in" | "out", anchor?: number) => {
      if (!duration) {
        return;
      }

      const currentSize = Math.max(timeWindow[1] - timeWindow[0], minWindowSize);
      const zoomFactor = direction === "in" ? ZOOM_IN_FACTOR : ZOOM_OUT_FACTOR;
      const proposedSize = currentSize * zoomFactor;
      const targetSize = direction === "in" ? Math.max(minWindowSize, proposedSize) : Math.min(duration, proposedSize);

      const focus = anchor ?? (timeWindow[0] + timeWindow[1]) / 2;
      const start = focus - targetSize / 2;
      setWindow(start, targetSize);
    },
    [duration, timeWindow, minWindowSize, setWindow]
  );

  const jumpToBoundary = useCallback(
    (position: "start" | "end") => {
      if (!duration || timeWindowSize === 0) {
        return;
      }

      if (position === "start") {
        setWindow(0, timeWindowSize, { updateSize: false });
      } else {
        setWindow(duration - timeWindowSize, timeWindowSize, { updateSize: false });
      }
    },
    [duration, timeWindowSize, setWindow]
  );
  const channelViewRef = useRef<HTMLDivElement>(null);

  const fetchChannelData = useCallback(async () => {
    try {
      setChannelDataLoading(true);
      const response = await apiClient.get<{ channel_data: Record<string, number[]> }>(`/analysis/results/${jobId}/channel-data`);
      setChannelData(response.data.channel_data);
    } catch (err) {
      console.error("Failed to load channel data:", err);
      // Channel data is optional, so don't set error state
    } finally {
      setChannelDataLoading(false);
    }
  }, [jobId]);

  const fetchResults = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.get<AnalysisResults>(`/analysis/results/${jobId}`);

      if (!response.data || !response.data.metadata) {
        throw new Error("Invalid results format");
      }

      // Analyze HFO time distribution
      if (response.data.hfo_events && response.data.hfo_events.length > 0) {
        const timeDistribution: Record<string, number> = {};
        const windowSize = 10; // 10-second windows
        const totalDuration = response.data.metadata.duration_seconds || 60;

        // Initialize time windows
        for (let i = 0; i < totalDuration; i += windowSize) {
          const windowKey = `${i}-${i + windowSize}s`;
          timeDistribution[windowKey] = 0;
        }

        // Count HFOs per time window
        response.data.hfo_events.forEach((hfo) => {
          const windowStart = Math.floor(hfo.start_time / windowSize) * windowSize;
          const windowKey = `${windowStart}-${windowStart + windowSize}s`;
          if (timeDistribution[windowKey] !== undefined) {
            timeDistribution[windowKey]++;
          }
        });

        // Analyze HFO types
        const typeCounts: Record<string, number> = {};
        response.data.hfo_events.forEach((event: HFOEvent) => {
          const eventType = event.type || "undefined";
          typeCounts[eventType] = (typeCounts[eventType] || 0) + 1;
        });
      }

      if (response.data.metadata.channels && response.data.metadata.channels.length > 0) {
        setSelectedChannels(response.data.metadata.channels);
      }

      const responseDuration = response.data.metadata.duration_seconds || 0;
      const initialWindowSize = responseDuration > 0 ? responseDuration : 10;
      const initialWindowEnd = responseDuration > 0 ? initialWindowSize : 0;
      setTimeWindow([0, initialWindowEnd]);
      setTimeWindowSize(initialWindowEnd);
      setCurrentChunk(0);

      setResults(response.data);
      setLoading(false);

      // Fetch channel data separately if not included in results
      if (!response.data.channel_data) {
        fetchChannelData();
      } else {
        setChannelData(response.data.channel_data);
      }
    } catch (err) {
      const error = err as { response?: { data?: { detail?: string } } };
      setError(error.response?.data?.detail || "Failed to load results");
      setLoading(false);
    }
  }, [jobId, fetchChannelData]);

  useEffect(() => {
    fetchResults();
  }, [fetchResults]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (!results || e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return;
      }

      switch (e.key) {
        case "ArrowLeft":
          e.preventDefault();
          panWindow(-1);
          break;
        case "ArrowRight":
          e.preventDefault();
          panWindow(1);
          break;
        case "+":
        case "=":
          e.preventDefault();
          zoomWindow("in");
          break;
        case "-":
        case "_":
          e.preventDefault();
          zoomWindow("out");
          break;
        case "Home":
          e.preventDefault();
          jumpToBoundary("start");
          break;
        case "End":
          e.preventDefault();
          jumpToBoundary("end");
          break;
        case "f":
        case "F":
          if (channelViewRef.current) {
            if (!document.fullscreenElement) {
              channelViewRef.current.requestFullscreen();
            } else {
              document.exitFullscreen();
            }
          }
          break;
        default:
          break;
      }
    };

    document.addEventListener("keydown", handleKeyPress);
    return () => document.removeEventListener("keydown", handleKeyPress);
  }, [results, panWindow, zoomWindow, jumpToBoundary, channelViewRef, setWindow]);

  useEffect(() => {
    const container = channelViewRef.current;
    if (!container || !results) {
      return;
    }

    const handleWheel = (event: WheelEvent) => {
      if (!event.ctrlKey && !event.metaKey && !event.shiftKey) {
        return;
      }

      event.preventDefault();

      const bounds = container.getBoundingClientRect();
      const positionRatio = bounds.width > 0 ? (event.clientX - bounds.left) / bounds.width : 0.5;
      const anchor = timeWindow[0] + (timeWindow[1] - timeWindow[0]) * positionRatio;
      if (event.deltaY < 0) {
        zoomWindow("in", anchor);
      } else {
        zoomWindow("out", anchor);
      }
    };

    container.addEventListener("wheel", handleWheel, { passive: false });
    return () => {
      container.removeEventListener("wheel", handleWheel);
    };
  }, [results, zoomWindow, timeWindow]);

  // Handle fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener("fullscreenchange", handleFullscreenChange);
    return () => document.removeEventListener("fullscreenchange", handleFullscreenChange);
  }, []);

  // Calculate HFO statistics by type
  const hfoStatsByType = useMemo(() => {
    if (!results) return null;

    const stats = {
      total: results.hfo_events.length,
      accepted: results.hfo_events.filter((e) => (e.type || "accepted") === "accepted").length,
      rejected: results.hfo_events.filter((e) => e.type === "rejected").length,
      rejected_long: results.hfo_events.filter((e) => e.type === "rejected_long").length,
      lfo_rejected: results.hfo_events.filter((e) => e.type === "lfo_rejected").length,
      noise_rejected: results.hfo_events.filter((e) => e.type === "noise_rejected").length,
    };
    return stats;
  }, [results]);

  // Filter HFO events by visible types
  const filteredHfoEvents = useMemo(() => {
    if (!results) return [];
    return results.hfo_events.filter((event) => {
      const eventType = event.type || "accepted";
      return visibleHFOTypes.includes(eventType);
    });
  }, [results, visibleHFOTypes]);

  const downloadResults = async (format: string) => {
    try {
      // For comprehensive report
      if (format === "report") {
        const response = await apiClient.get<{ download_url: string }>(`/analysis/download/${jobId}?format=report`);
        if (response.data && response.data.download_url) {
          window.open(response.data.download_url, "_blank");
        }
        return;
      }

      const response = await apiClient.get<{ download_url: string }>(`/analysis/download/${jobId}?format=${format}`);

      if (response.data && response.data.download_url) {
        window.open(response.data.download_url, "_blank");
      } else {
        handleLocalDownload(format);
      }
    } catch {
      handleLocalDownload(format);
    }
  };

  const handleLocalDownload = (format: string) => {
    if (!results) return;

    const data = format === "json" ? JSON.stringify(results, null, 2) : convertToCSV(results.hfo_events);
    const blob = new Blob([data], {
      type: format === "json" ? "application/json" : "text/csv",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `analysis_results_${jobId}.${format}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const convertToCSV = (events: HFOEvent[]) => {
    const headers = ["Channel", "Start Time", "End Time", "Peak Frequency", "Amplitude"];
    const rows = events.map((e) => [e.channel, e.start_time, e.end_time, e.peak_frequency, e.amplitude]);
    return [headers, ...rows].map((row) => row.join(",")).join("\n");
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading analysis results...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 p-6 rounded-lg">
        <p className="text-red-800">{error}</p>
      </div>
    );
  }

  if (!results) return null;

  return (
    <div className="flex flex-col h-full bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-xl font-bold text-gray-900">HFO Analysis Results</h1>
            <p className="text-sm text-gray-600">{results.metadata.filename}</p>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={() => downloadResults("csv")}
              className="px-3 py-1 bg-black text-white text-xs rounded hover:bg-gray-800 transition-colors flex items-center gap-1"
              title="Download HFO events"
            >
              <Download className="w-3 h-3" />
              Export
            </button>
            {onClose && (
              <button onClick={onClose} className="text-gray-500 hover:text-gray-700 text-2xl" title="Close">
                ×
              </button>
            )}
          </div>
        </div>
      </header>

      {/* Main Graph View */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Time Navigation Controls */}
        <div className="px-4 pt-4">
          <TimeNavigationControls
            timeWindow={timeWindow}
            timeWindowSize={timeWindowSize}
            totalDuration={results.metadata.duration_seconds}
            currentChunk={currentChunk}
            totalChunks={Math.max(1, Math.ceil(results.metadata.duration_seconds / CHUNK_SECONDS))}
            onTimeWindowChange={(window) => setWindow(window[0], window[1] - window[0], { updateSize: false })}
            onTimeWindowSizeChange={(size) => {
              const currentCenter = (timeWindow[0] + timeWindow[1]) / 2;
              const newStart = currentCenter - size / 2;
              setWindow(newStart, size);
            }}
            onReset={() => {
              const fullDuration = results.metadata.duration_seconds || 0;
              setWindow(0, fullDuration);
              setSelectedChannels(results.metadata.channels);
            }}
            onFullscreen={() => {
              if (channelViewRef.current) {
                if (!document.fullscreenElement) {
                  channelViewRef.current.requestFullscreen();
                } else {
                  document.exitFullscreen();
                }
              }
            }}
          />

          {/* Position indicator */}
          <div className="mt-2 bg-gray-100 rounded p-2">
            <div className="relative h-2 bg-gray-300 rounded">
              <div
                className="absolute h-full bg-black rounded transition-all duration-300"
                style={{
                  left: `${(timeWindow[0] / results.metadata.duration_seconds) * 100}%`,
                  width: `${((timeWindow[1] - timeWindow[0]) / results.metadata.duration_seconds) * 100}%`,
                }}
              />
            </div>
            <div className="flex justify-between text-xs text-gray-600 mt-1">
              <span>0s</span>
              <span className="font-semibold">
                {timeWindow[0].toFixed(1)}s - {timeWindow[1].toFixed(1)}s
              </span>
              <span>{results.metadata.duration_seconds.toFixed(1)}s</span>
            </div>
          </div>
        </div>

        {/* Multi-Channel View */}
        <div ref={channelViewRef} className={`flex-1 flex overflow-hidden bg-white rounded-lg shadow-sm ${isFullscreen ? "m-0" : "m-4"}`}>
          {/* Channel Selector Sidebar */}
          {!isFullscreen && (
            <ChannelSelector
              channels={results.metadata.channels}
              selectedChannels={selectedChannels}
              onChannelToggle={(channel) => {
                setSelectedChannels((prev) => (prev.includes(channel) ? prev.filter((ch) => ch !== channel) : [...prev, channel]));
              }}
              onSelectAll={() => setSelectedChannels(results.metadata.channels)}
              onClearAll={() => setSelectedChannels([])}
              hfoCountByChannel={results.metadata.channels.reduce((acc, channel) => {
                acc[channel] = results.hfo_events.filter((h) => h.channel === channel).length;
                return acc;
              }, {} as Record<string, number>)}
            />
          )}

          {/* Channel Grid Display */}
          <div className="flex-1 flex flex-col">
            <div className="flex-shrink-0 h-14 bg-gray-50 border-b border-gray-200 px-4 flex items-center justify-between">
              <div className="flex items-center gap-4">
                <h2 className="text-sm font-semibold text-gray-900">Channel View</h2>
                <span className="text-xs text-gray-600 bg-white px-2 py-1 rounded">
                  {selectedChannels.length}/{results.metadata.channels.length} channels
                </span>
                {hfoStatsByType && (
                  <span className="text-xs text-gray-600 bg-white px-2 py-1 rounded">
                    {filteredHfoEvents.length} of {hfoStatsByType.total} HFOs
                  </span>
                )}
              </div>
              <div className="flex items-center gap-2">
                {isFullscreen && (
                  <button
                    onClick={() => document.exitFullscreen()}
                    className="px-3 py-1 bg-gray-700 text-white text-xs rounded hover:bg-gray-600 transition-colors"
                  >
                    Exit Fullscreen (F)
                  </button>
                )}
              </div>
            </div>

            <div className="flex-1 overflow-auto">
              {channelData ? (
                <ChannelGrid
                  channelData={channelData}
                  visibleChannels={selectedChannels}
                  timeWindow={timeWindow}
                  samplingRate={results.sampling_info?.effective_sampling_rate || results.metadata.sampling_rate}
                  hfoEvents={filteredHfoEvents}
                  showHFOMarkers={true}
                  samplingInfo={results.sampling_info}
                />
              ) : channelDataLoading ? (
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="mt-2 text-sm text-gray-600">Loading channel data...</p>
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <p className="text-gray-600">Channel data not available</p>
                    <button onClick={fetchChannelData} className="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                      Load Channel Data
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
